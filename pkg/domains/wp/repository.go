package wp

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/onwa/pkg/config"
	customerrors "github.com/onwa/pkg/customErrors"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/state"
	"github.com/onwa/pkg/utils"
	"gorm.io/gorm"
)

type Repository interface {
	sessionCreate(ctx context.Context, req dtos.RequestForCreateSession) (dtos.ResponseForSessionCreate, error)
	sessionStop(ctx context.Context) error

	requestWpCode(ctx context.Context, req dtos.RequestForWPCode) (string, error)
	checkDeviceWithSession(ctx context.Context) (dtos.ResponseForCheckDeviceWithSession, error)
	checkDeviceWithSessionWithoutTimeout(ctx context.Context) (dtos.ResponseForCheckDeviceWithSession, error)

	getProfilePhoto(ctx context.Context, phone string, for_session bool) (dtos.CoreResponseForProfilePhone, error)

	phoneNumberCreate(ctx context.Context, out *[]entities.Number) error
	phoneNumberGetAll(ctx context.Context, out *[]entities.Number) error
	phoneNumberDelete(ctx context.Context, id uuid.UUID) error
	phoneNumberCountForUser(ctx context.Context, user_id uuid.UUID) (int64, error)

	presenceGetByID(ctx context.Context, presence_id string, out *entities.Presence) error
	presenceGetAll(ctx context.Context, status, phone string, out *[]entities.Presence) error
	presenceStart(ctx context.Context, req dtos.RequestForPresenceStart) (dtos.ResponseForPresenceStart, error)
	presenceStop(ctx context.Context, presence_id string) error
	presenceGetLast(ctx context.Context, presence_id string) (dtos.CoreResponseForPresenceLast, error)
	reportGet(ctx context.Context, page, perpage int, time_range, start_date, end_date, presence_id string) (*dtos.CoreResponseForPresenceHistory, error)

	userGet(ctx context.Context, out *entities.User) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// -----> Session Start

func (r *repository) sessionCreate(ctx context.Context, req dtos.RequestForCreateSession) (dtos.ResponseForSessionCreate, error) {
	var (
		resp         dtos.ResponseForSessionCreate
		core_resp    dtos.CoreResponseForSessionCreate
		http_payload = make(map[string]interface{})
		current_user entities.User
	)

	tx := r.db.WithContext(ctx).Begin()

	if err := tx.Model(&entities.User{}).
		Where("id=?", state.GetCurrentUserID(ctx)).
		First(&current_user).Error; err != nil {
		tx.Rollback()
		return resp, err
	}

	utils.HttpDeleteRequest(fmt.Sprintf("/session/%s", current_user.SessionID.String()))

	if os.Getenv("MODE") == "dev" {
		http_payload["ip"] = config.InitConfig().App.LocalIP
	} else {
		http_payload["ip"] = state.GetCurrentIP(ctx)
	}

	http_payload["e_164_phone_number"] = req.E164PhoneNumber
	http_payload["use_proxy"] = true
	http_payload["auto_reconnect"] = true
	http_payload["device_info"] = current_user.ID.String()
	http_payload["timezone"] = current_user.TimeZone

	body2, err := utils.HttpPostRequest("/session", http_payload)
	if err != nil {
		tx.Rollback()
		return resp, err
	}

	if err := json.Unmarshal(*body2, &core_resp); err != nil {
		tx.Rollback()
		return resp, err
	}

	if core_resp.Status != "initialized" {
		tx.Rollback()
		return resp, errors.New("session could not be created")
	}

	current_user.SessionID = core_resp.ID
	current_user.E164PhoneNumber = req.E164PhoneNumber
	current_user.E164PhoneNumberForLicense = req.E164PhoneNumber
	current_user.LastIP = state.GetCurrentIP(ctx)

	if err := tx.Save(&current_user).Error; err != nil {
		tx.Rollback()
		return resp, err
	}
	tx.Commit()

	resp.SessionID = core_resp.ID.String()

	return resp, nil
}

func (r *repository) sessionStop(ctx context.Context) error {
	var (
		current_user entities.User
		presences    []entities.Presence
		numbers      []entities.Number
	)

	tx := r.db.WithContext(ctx).Begin()
	if err := tx.Model(&entities.User{}).
		Where("id=?", state.GetCurrentUserID(ctx)).
		First(&current_user).Error; err != nil {
		tx.Rollback()
		return err
	}

	current_user_session_id := current_user.SessionID

	current_user.SessionID = uuid.Nil
	current_user.E164PhoneNumber = ""

	if err := tx.Save(&current_user).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Model(&entities.Presence{}).
		Where("session_id=?", current_user_session_id).
		Where("user_id=?", current_user.ID.String()).
		Find(&presences).Error; err != nil && err != gorm.ErrRecordNotFound {
		tx.Rollback()
		return err
	}

	if len(presences) != 0 {
		if err := tx.Model(&entities.Presence{}).
			Where("session_id=?", state.GetCurrentSessionID(ctx)).
			Where("user_id=?", current_user.ID.String()).
			Delete(&entities.Presence{}).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Model(&entities.Number{}).
		Where("user_id=?", current_user.ID.String()).
		Find(&numbers).Error; err != nil && err != gorm.ErrRecordNotFound {
		tx.Rollback()
		return err
	}

	if len(numbers) != 0 {
		if err := tx.Model(&entities.Number{}).
			Where("user_id=?", current_user.ID.String()).
			Delete(&entities.Number{}).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	_, err := utils.HttpDeleteRequest(fmt.Sprintf("/sessions/%s", current_user_session_id.String()))
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

// Session End <-----

func (r *repository) requestWpCode(ctx context.Context, req dtos.RequestForWPCode) (string, error) {

	var (
		resp         dtos.WahaResponseAuthReqCode
		m            = make(map[string]interface{})
		current_user entities.User
	)

	// find user
	if err := r.db.Model(&entities.User{}).
		Where("id = ?", state.GetCurrentUserID(ctx)).
		First(&current_user).Error; err != nil {
		return "", err
	}

	if current_user.SessionID == uuid.Nil {
		return "", errors.New("session_id not found")
	}

	m["phone"] = req.E164PhoneNumber
	m["e_164_phone_number"] = req.E164PhoneNumber
	m["ip"] = state.GetCurrentIP(ctx)
	m["session_id"] = current_user.SessionID.String()

	body, err := utils.HttpPostRequest(fmt.Sprintf("/session/%s/login-code", current_user.SessionID), m)
	if err != nil {
		return "", err
	}

	if err := json.Unmarshal(*body, &resp); err != nil {
		return "", err
	}

	current_user.LastIP = state.GetCurrentIP(ctx)

	r.db.WithContext(ctx).Save(&current_user)

	// TODO: control
	//s.repository.DeletePresence(ctx)

	return resp.Code, nil
}

func (r *repository) checkDeviceWithSession(ctx context.Context) (dtos.ResponseForCheckDeviceWithSession, error) {
	var (
		current_user entities.User
		resp         dtos.ResponseForCheckDeviceWithSession
		core_resp    dtos.CoreResponseForCheckDevice
	)

	r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("id=?", state.GetCurrentUserID(ctx)).
		First(&current_user)

	defer func() {
		if resp.Status == "disconnected" || resp.Status == "session_not_found" {
			r.db.WithContext(ctx).Model(&entities.Presence{}).Where("session_id=?", current_user.SessionID).Delete(&entities.Presence{})

			r.db.WithContext(ctx).Model(&entities.Number{}).Where("user_id=?", current_user.ID.String()).Delete(&entities.Number{})
		}
	}()

	if current_user.SessionID == uuid.Nil {
		resp.SessionID = ""
		resp.Status = "session_not_found"
		return resp, errors.New("session_id not found")
	}

	body, _, err := utils.HttpGetRequest(fmt.Sprintf("/session/%s/check-device", current_user.SessionID))
	if err != nil {
		resp.SessionID = current_user.SessionID.String()
		resp.Status = "disconnected"
		return resp, err
	}

	if err := json.Unmarshal(*body, &core_resp); err != nil {
		resp.SessionID = current_user.SessionID.String()
		resp.Status = "disconnected"
		return resp, err
	}

	if core_resp.Connected {
		resp.Status = "connected"
		current_user.RegID = core_resp.RegID
		current_user.LastConnectionStatus = "connected"
		current_user.LastConnectionCheckTime = time.Now()
	} else {
		resp.Status = "disconnected"
		current_user.RegID = ""
		current_user.LastConnectionStatus = "disconnected"
		current_user.LastConnectionCheckTime = time.Now()
	}
	resp.SessionID = core_resp.SessionID

	r.db.Save(&current_user)

	return resp, nil
}

func (r *repository) checkDeviceWithSessionWithoutTimeout(ctx context.Context) (dtos.ResponseForCheckDeviceWithSession, error) {
	var (
		current_user entities.User
		resp         dtos.ResponseForCheckDeviceWithSession
		core_resp    dtos.CoreResponseForCheckDevice
	)

	r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("id=?", state.GetCurrentUserID(ctx)).
		First(&current_user)

	defer func() {
		if resp.Status == "disconnected" || resp.Status == "session_not_found" {
			r.db.WithContext(ctx).Model(&entities.Presence{}).Where("session_id=?", current_user.SessionID).Delete(&entities.Presence{})

			r.db.WithContext(ctx).Model(&entities.Number{}).Where("user_id=?", current_user.ID.String()).Delete(&entities.Number{})
		}
	}()

	if current_user.SessionID == uuid.Nil {
		resp.SessionID = ""
		resp.Status = "session_not_found"
		resp.PhoneNumber = current_user.E164PhoneNumber
		return resp, errors.New("session_id not found")
	}

	body, _, err := utils.HttpGetRequest(fmt.Sprintf("/session/%s/active-device", current_user.SessionID))
	if err != nil {
		resp.SessionID = current_user.SessionID.String()
		resp.Status = "disconnected"
		resp.PhoneNumber = current_user.E164PhoneNumber
		return resp, err
	}

	if err := json.Unmarshal(*body, &core_resp); err != nil {
		resp.SessionID = current_user.SessionID.String()
		resp.Status = "disconnected"
		resp.PhoneNumber = current_user.E164PhoneNumber
		return resp, err
	}

	if core_resp.Connected {
		resp.Status = "connected"
		current_user.RegID = core_resp.RegID
		current_user.LastConnectionStatus = "connected"
		current_user.LastConnectionCheckTime = time.Now()
	} else {
		resp.Status = "disconnected"
		current_user.RegID = ""
		current_user.LastConnectionStatus = "disconnected"
		current_user.LastConnectionCheckTime = time.Now()
	}
	resp.SessionID = core_resp.SessionID
	resp.PhoneNumber = current_user.E164PhoneNumber

	r.db.Save(&current_user)

	return resp, nil
}

func (r *repository) getProfilePhoto(ctx context.Context, phone string, for_session bool) (dtos.CoreResponseForProfilePhone, error) {
	var (
		current_user entities.User
		resp         dtos.CoreResponseForProfilePhone
		m            = make(map[string]interface{})
	)

	if err := r.db.Model(&entities.User{}).
		Where("id=?", state.GetCurrentUserID(ctx)).
		First(&current_user).Error; err != nil {
		return resp, err
	}

	if for_session {
		m["phone"] = ""
	} else {
		m["phone"] = phone
	}

	body, err := utils.HttpPostRequest(fmt.Sprintf("/session/%s/profile-photo", current_user.SessionID), m)
	if err != nil {
		return resp, err
	}

	if err := json.Unmarshal(*body, &resp); err != nil {
		return resp, err
	}

	return resp, nil
}

// -----> Phone Number Start

func (r *repository) phoneNumberCreate(ctx context.Context, out *[]entities.Number) error {
	return r.db.WithContext(ctx).
		Model(&entities.Number{}).
		Create(out).Error
}

func (r *repository) phoneNumberGetAll(ctx context.Context, out *[]entities.Number) error {
	err := r.db.WithContext(ctx).
		Model(&entities.Number{}).
		Where("user_id=?", state.GetCurrentUserID(ctx)).
		Find(out).Error

	return err
}

func (r *repository) phoneNumberDelete(ctx context.Context, id uuid.UUID) error {
	var number entities.Number
	if err := r.db.WithContext(ctx).
		Model(&entities.Number{}).
		Where("id=? AND user_id=?", id, state.GetCurrentUserID(ctx)).
		First(&number).Error; err != nil {
		return err
	}

	chat_id := fmt.Sprintf("%<EMAIL>", number.RawPhoneNumber)

	if err := r.db.WithContext(ctx).
		Model(&entities.Number{}).
		Delete(&number).Error; err != nil {
		return err
	}

	r.db.WithContext(ctx).
		Model(&entities.Presence{}).
		Where("user_id = ? AND chat_id = ?", state.GetCurrentUserID(ctx), chat_id).
		Updates(map[string]interface{}{
			"done": true,
		})

	return nil
}

func (r *repository) phoneNumberCountForUser(ctx context.Context, user_uuid uuid.UUID) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).
		Model(&entities.Number{}).
		Where("user_id=?", user_uuid).
		Count(&count).Error; err != nil {
		return count, err
	}

	return count, nil
}

// Phone Number End <-----

// -----> Presence Start

func (r *repository) presenceGetByID(ctx context.Context, presence_id string, out *entities.Presence) error {
	return r.db.WithContext(ctx).
		Model(&entities.Presence{}).
		Where("user_id = ?", state.GetCurrentUserID(ctx)).
		Where("id = ?", presence_id).
		Where("done = FALSE"). // tamamlanmış değilse
		First(out).Error
}

func (r *repository) presenceGetLast(ctx context.Context, presence_id string) (dtos.CoreResponseForPresenceLast, error) {
	//-----> find presence
	var (
		presence_control entities.Presence
		url              = fmt.Sprintf("/session/")
		core_response    dtos.CoreResponseForPresenceLast
		current_user     entities.User
	)

	r.db.WithContext(ctx).
		Model(&entities.Presence{}).
		Where("id = ? AND user_id = ?", presence_id, state.GetCurrentUserID(ctx)).
		Order("created_at DESC").
		First(&presence_control)

	if presence_control.ID == uuid.Nil {
		return core_response, errors.New("presence not found")
	}

	r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("id=?", state.GetCurrentUserID(ctx)).
		First(&current_user)

	url = url + current_user.SessionID.String() + "/presence/last"

	url = url + "?phone=" + presence_control.Phone

	body, _, err := utils.HttpGetRequest(url)
	if err != nil {
		return core_response, err
	}

	if err := json.Unmarshal(*body, &core_response); err != nil {
		return core_response, err
	}

	return core_response, nil
}

func (r *repository) presenceGetAll(ctx context.Context, status, phone string, out *[]entities.Presence) error {
	query := r.db.WithContext(ctx).
		Model(&entities.Presence{}).
		Select("*,"+"started_at AT TIME ZONE ? AS started_at, "+"ended_at AT TIME ZONE ? AS ended_at", state.CurrentTime(ctx), state.CurrentTime(ctx)).
		Where("user_id = ?", state.GetCurrentUserID(ctx))

	if status != "" {
		if status == "active" {
			query = query.Where("done = FALSE")
		} else {
			query = query.Where("done = TRUE")
		}
	}

	if phone != "" {
		query = query.Where("phone LIKE ?", "%"+phone+"%")
	}

	err := query.Find(out).Error

	return err
}

func (r *repository) presenceStart(ctx context.Context, req dtos.RequestForPresenceStart) (dtos.ResponseForPresenceStart, error) {
	var (
		resp                dtos.ResponseForPresenceStart
		dublicate_control   entities.Presence
		current_user        entities.User
		current_user_detail entities.UserDetail
	)

	tx := r.db.WithContext(ctx).Begin()

	if err := tx.Model(&entities.User{}).
		Where("id=?", state.GetCurrentUserID(ctx)).
		First(&current_user).Error; err != nil {
		tx.Rollback()
		return resp, err
	}

	session_id := current_user.SessionID

	if err := tx.Model(&entities.Presence{}).Debug().
		Where("user_id = ?", state.GetCurrentUserID(ctx)).
		Where("session_id = ?", current_user.SessionID).
		Where("phone = ?", req.Phone).
		Where("done = FALSE"). // tamamlanmış değilse
		First(&dublicate_control).Error; err != nil && err != gorm.ErrRecordNotFound {
		tx.Rollback()
		return resp, err
	}

	if dublicate_control.ID != uuid.Nil {
		tx.Rollback()
		return resp, errors.New(customerrors.ErrPresenceAlraedyExist)
	}

	var m = make(map[string]interface{})
	m["phone"] = req.Phone
	utils.HttpPostRequest(fmt.Sprintf("/session/%s/subscribe", session_id), m)

	if err := tx.Model(&entities.UserDetail{}).
		Where("user_id=?", state.GetCurrentUserID(ctx)).
		First(&current_user_detail).Error; err != nil {
		tx.Rollback()
		return resp, err
	}

	var presence entities.Presence

	presence.SessionID = session_id
	presence.UserID = state.GetCurrentUserID(ctx)
	presence.DeviceID = state.GetCurrentDeviceID(ctx)
	presence.TimeZone = state.CurrentTime(ctx)
	presence.Phone = req.Phone
	presence.StartedAt = time.Now()
	presence.NotificationAfterOffline = req.NotifActive
	var push_notif_s string

	if state.GetCurrentPushNotif(ctx) == "" {
		push_notif_s = current_user.PushNotifToken
	} else {
		push_notif_s = state.GetCurrentPushNotif(ctx)
	}

	presence.PushNotifToken = push_notif_s
	presence.ContactName = req.ContactName
	presence.ContactID = req.ContactID
	presence.Local = current_user.PhoneLanguage

	if err := tx.Model(&entities.Presence{}).
		Create(&presence).Error; err != nil {
		tx.Rollback()
		return resp, err
	}
	var current_license entities.License
	if err := tx.Model(&entities.License{}).
		Where("user_id = ?", state.GetCurrentUserID(ctx)).
		Order("created_at DESC").
		First(&current_license).Error; err != nil {
		tx.Rollback()
		return resp, err
	}

	// if user have lisence type 1 and end date is not expired, we start new license
	if current_license.LicenseType == 1 {
		if current_license.EndDate.Unix() < time.Now().Unix() && current_license.TotalTime != 0 {
			var new_license entities.License
			new_license.UserID = state.GetCurrentUserID(ctx)
			new_license.TotalTime = 0
			new_license.EndDate = time.Now().Add(time.Hour * time.Duration(current_license.TotalTime))
			new_license.LicenseType = 1
			new_license.PhoneNumberLimit = 3

			if err := tx.Model(&entities.License{}).
				Create(&new_license).Error; err != nil {
				tx.Rollback()
				return resp, err
			}
		}
	}

	tx.Commit()

	resp.PresenceID = presence.ID.String()
	resp.Status = "started"

	return resp, nil
}

func (r *repository) presenceStop(ctx context.Context, presence_id string) error {

	user_id := state.GetCurrentUserID(ctx)
	var current_presence entities.Presence

	r.db.WithContext(ctx).
		Model(&entities.Presence{}).
		Where("id", presence_id).
		First(&current_presence)

	if current_presence.ID == uuid.Nil {
		return errors.New("presence_not_found")
	}

	if current_presence.Done {
		return errors.New("presence_already_done")

	}

	if err := r.db.WithContext(ctx).
		Model(&entities.Presence{}).
		Where("id", current_presence.ID).
		Where("user_id=?", user_id).
		Updates(map[string]interface{}{
			"done":     true,
			"ended_at": time.Now(),
		}).Error; err != nil {
		return err
	}

	//TODO: core'a istek at, durdurmak için

	utils.HttpDeleteRequest(fmt.Sprintf("/session/%s/subscription/%s", current_presence.SessionID, current_presence.Phone))

	return nil
}

// Presence End <-----

// -----> Report Start

func (r *repository) reportGet(ctx context.Context, page, perpage int, time_range, start_date, end_date, presence_id string) (*dtos.CoreResponseForPresenceHistory, error) {
	var (
		url              = fmt.Sprintf("/session/")
		presence_control entities.Presence
		core_response    dtos.CoreResponseForPresenceHistory
		current_user     entities.User
	)

	r.db.WithContext(ctx).
		Model(&entities.Presence{}).
		Where("user_id=?", state.GetCurrentUserID(ctx)).
		Where("id=?", presence_id).
		First(&presence_control)

	if presence_control.ID == uuid.Nil {
		return &core_response, errors.New("presence not found")
	}

	r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("id=?", state.GetCurrentUserID(ctx)).
		First(&current_user)

	url = url + current_user.SessionID.String() + "/presence/history"

	converted_page := strconv.Itoa(page)
	converted_per_page := strconv.Itoa(perpage)

	if time_range != "" {
		url = url + "?time_range=" + time_range
	} else {
		url = url + "?start_date=" + start_date + "&end_date=" + end_date
	}

	// url = url + "?time_range=" + time_range
	url = url + "&phone=" + presence_control.Phone
	url = url + "&page=" + converted_page
	url = url + "&per_page=" + converted_per_page

	body, _, err := utils.HttpGetRequest(url)
	if err != nil {
		return &core_response, err
	}

	if err := json.Unmarshal(*body, &core_response); err != nil {
		return &core_response, err
	}

	return &core_response, nil
}

// Report End <-----

func (r *repository) userGet(ctx context.Context, out *entities.User) error {
	return r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("id=?", state.GetCurrentUserID(ctx)).
		First(out).Error
}
