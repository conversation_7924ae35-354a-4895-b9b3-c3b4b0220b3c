package user

import (
	"context"
	"log"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/onwa/pkg/cache"
	"github.com/onwa/pkg/config"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/state"
	"github.com/onwa/pkg/utils"
	"gorm.io/gorm"
)

type Repository interface {
	login(ctx context.Context, req dtos.RequestForLogin) (dtos.ResponseForLogin, error)
	getUserWithDeviceID(ctx context.Context, device_id string, out *entities.User) error // -----> not used
	getCurrentUser(ctx context.Context, out *entities.User) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) login(ctx context.Context, req dtos.RequestForLogin) (dtos.ResponseForLogin, error) {
	var (
		user entities.User
		resp dtos.ResponseForLogin
	)

	if err := r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("device_id = ?", req.DeviceID).
		First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			var (
				new_user        entities.User
				new_user_detail entities.UserDetail
				new_license     entities.License
			)

			new_user.DeviceID = req.DeviceID
			new_user.PushNotifToken = req.PushNotifToken
			new_user.TimeZone = req.TimeZone
			new_user.PurchaseID = req.PurchaseID
			new_user.PhoneLanguage = req.PhoneLanguage
			new_user.OS = req.OS
			new_user.LastVersionName = req.LastVersionName
			new_user.LastVersionBuildNumber = req.LastVersionBuildNumber

			tx := r.db.WithContext(ctx).Begin()

			if err := tx.Model(&entities.User{}).
				Create(&new_user).Error; err != nil {
				tx.Rollback()
				return resp, err
			}

			new_user_detail.UserID = new_user.ID

			if err := tx.Model(&entities.UserDetail{}).Create(&new_user_detail).Error; err != nil {
				tx.Rollback()
				return resp, err
			}

			new_license.UserID = new_user.ID
			new_license.LicenseType = 1 //-----> Default Free License
			new_license.PhoneNumberLimit = 3

			new_license.TotalTime = 3

			if err := tx.Model(&entities.License{}).Create(&new_license).Error; err != nil {
				tx.Rollback()
				return resp, err
			}

			token, err := r.createJWT(ctx, &new_user)
			if err != nil {
				tx.Rollback()
				return resp, err
			}

			resp.ID = new_user.ID
			resp.Token = token

			tx.Commit()

			return resp, nil
		}
		return resp, err
	}

	log.Println("login hesap var")

	token, err := r.createJWT(ctx, &user)
	if err != nil {
		return resp, err
	}

	user.PushNotifToken = req.PushNotifToken
	user.TimeZone = req.TimeZone
	user.PurchaseID = req.PurchaseID
	//user.PhoneLanguage = req.PhoneLanguage

	if user.PhoneLanguage != req.PhoneLanguage {
		user.PhoneLanguage = req.PhoneLanguage
		r.db.WithContext(ctx).
			Model(&entities.Presence{}).
			Where("user_id=?", user.ID).
			Updates(map[string]interface{}{"local": req.PhoneLanguage})
	}

	user.OS = req.OS
	user.LastVersionName = req.LastVersionName
	user.LastVersionBuildNumber = req.LastVersionBuildNumber

	if user.PushNotifToken != "" {
		var presence []entities.Presence
		r.db.WithContext(ctx).
			Model(&entities.Presence{}).
			Where("user_id=?", user.ID).
			Find(&presence)

		for _, p := range presence {
			if p.PushNotifToken == "" {
				r.db.WithContext(ctx).
					Model(&entities.Presence{}).
					Where("id=?", p.ID).
					Update("push_notif_token", req.PushNotifToken)
			}
		}
	}

	// ***** For IOS ***** //
	/*if req.OS == "ios3" {
		user.SessionName = "905452267593-AP31.2"
	}*/

	if err := r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("device_id = ?", req.DeviceID).
		Save(&user).Error; err != nil {
		return resp, err
	}

	resp.ID = user.ID
	resp.Token = token

	return resp, nil
}

func (r *repository) getUserWithDeviceID(ctx context.Context, device_id string, out *entities.User) error {
	if err := r.db.WithContext(ctx).
		Model(&entities.User{}).
		Where("device_id = ?", device_id).
		First(out).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) getCurrentUser(ctx context.Context, out *entities.User) error {
	log.Println("timezone: ", state.CurrentTime(ctx))
	err := r.db.WithContext(ctx).
		Model(&entities.User{}).
		Select("*,"+"last_connection_check_time AT TIME ZONE ? AS last_connection_check_time", state.CurrentTime(ctx)).
		Where("id = ?", state.GetCurrentUserID(ctx)).
		First(out).Error
	return err
}

func (r *repository) createJWT(ctx context.Context, out *entities.User) (string, error) {
	claims := utils.JwtCustomClaim{
		UserID:          out.ID.String(),
		DeviceID:        out.DeviceID,
		PushNotifToken:  out.PushNotifToken,
		PurchaseID:      out.PurchaseID,
		E164PhoneNumber: out.E164PhoneNumber,
		RegID:           out.RegID,
		Timezone:        out.TimeZone,
		PhoneLanguage:   out.PhoneLanguage,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * 24)),
		},
	}

	claims.InReview = false

	log.Println("createJWT 1")

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signed_str, err := token.SignedString([]byte(config.InitConfig().App.JwtSecret))
	if err != nil {
		return "", err
	}

	log.Println("createJWT before")

	if err := cache.SetUserSession(ctx, out.ID.String(), signed_str, (time.Hour * 24)); err != nil {
		log.Println("createJWT error: ", err.Error())
		return "", err
	}

	log.Println("createJWT after")

	return signed_str, nil
}
